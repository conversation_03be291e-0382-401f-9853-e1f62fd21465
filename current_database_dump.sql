--
-- PostgreSQL database dump
--

-- Dumped from database version 17.1
-- Dumped by pg_dump version 17.1

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: archive; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA archive;


ALTER SCHEMA archive OWNER TO postgres;

--
-- Name: assessment; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA assessment;


ALTER SCHEMA assessment OWNER TO postgres;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA auth;


ALTER SCHEMA auth OWNER TO postgres;

--
-- Name: btree_gin; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS btree_gin WITH SCHEMA public;


--
-- Name: EXTENSION btree_gin; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION btree_gin IS 'support for indexing common datatypes in GIN';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: cleanup_expired_idempotency_cache(); Type: FUNCTION; Schema: assessment; Owner: postgres
--

CREATE FUNCTION assessment.cleanup_expired_idempotency_cache() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM assessment.idempotency_cache 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION assessment.cleanup_expired_idempotency_cache() OWNER TO postgres;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: analysis_jobs; Type: TABLE; Schema: archive; Owner: postgres
--

CREATE TABLE archive.analysis_jobs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    job_id character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    status character varying(50) DEFAULT 'queued'::character varying NOT NULL,
    result_id uuid,
    error_message text,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    assessment_name character varying(255) DEFAULT 'AI-Driven Talent Mapping'::character varying NOT NULL,
    priority integer DEFAULT 0 NOT NULL,
    retry_count integer DEFAULT 0 NOT NULL,
    max_retries integer DEFAULT 3 NOT NULL,
    processing_started_at timestamp with time zone,
    CONSTRAINT analysis_jobs_assessment_name_check CHECK (((assessment_name)::text = ANY ((ARRAY['AI-Driven Talent Mapping'::character varying, 'AI-Based IQ Test'::character varying, 'Custom Assessment'::character varying])::text[]))),
    CONSTRAINT analysis_jobs_status_check CHECK (((status)::text = ANY ((ARRAY['queued'::character varying, 'processing'::character varying, 'completed'::character varying, 'failed'::character varying])::text[])))
);


ALTER TABLE archive.analysis_jobs OWNER TO postgres;

--
-- Name: analysis_results; Type: TABLE; Schema: archive; Owner: postgres
--

CREATE TABLE archive.analysis_results (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    assessment_data jsonb,
    persona_profile jsonb,
    status character varying(50) DEFAULT 'completed'::character varying NOT NULL,
    error_message text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    assessment_name character varying(255) DEFAULT 'AI-Driven Talent Mapping'::character varying NOT NULL,
    CONSTRAINT analysis_results_assessment_name_check CHECK (((assessment_name)::text = ANY ((ARRAY['AI-Driven Talent Mapping'::character varying, 'AI-Based IQ Test'::character varying, 'Custom Assessment'::character varying])::text[]))),
    CONSTRAINT analysis_results_status_check CHECK (((status)::text = ANY ((ARRAY['completed'::character varying, 'processing'::character varying, 'failed'::character varying])::text[])))
);


ALTER TABLE archive.analysis_results OWNER TO postgres;

--
-- Name: idempotency_cache; Type: TABLE; Schema: assessment; Owner: postgres
--

CREATE TABLE assessment.idempotency_cache (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    idempotency_key character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    request_hash character varying(64) NOT NULL,
    response_data jsonb NOT NULL,
    status_code integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    expires_at timestamp with time zone NOT NULL
);


ALTER TABLE assessment.idempotency_cache OWNER TO postgres;

--
-- Name: user_profiles; Type: TABLE; Schema: auth; Owner: postgres
--

CREATE TABLE auth.user_profiles (
    user_id uuid NOT NULL,
    full_name character varying(100),
    date_of_birth date,
    gender character varying(10),
    school_id integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT user_profiles_gender_check CHECK (((gender)::text = ANY ((ARRAY['male'::character varying, 'female'::character varying])::text[])))
);


ALTER TABLE auth.user_profiles OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: auth; Owner: postgres
--

CREATE TABLE auth.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    username character varying(100),
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    user_type character varying(20) DEFAULT 'user'::character varying NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    token_balance integer DEFAULT 0 NOT NULL,
    last_login timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT users_token_balance_check CHECK ((token_balance >= 0)),
    CONSTRAINT users_user_type_check CHECK (((user_type)::text = ANY ((ARRAY['user'::character varying, 'admin'::character varying, 'superadmin'::character varying, 'moderator'::character varying])::text[])))
);


ALTER TABLE auth.users OWNER TO postgres;

--
-- Name: schools; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.schools (
    id integer NOT NULL,
    name character varying(200) NOT NULL,
    address text,
    city character varying(100),
    province character varying(100),
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.schools OWNER TO postgres;

--
-- Name: schools_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.schools_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.schools_id_seq OWNER TO postgres;

--
-- Name: schools_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.schools_id_seq OWNED BY public.schools.id;


--
-- Name: schools id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schools ALTER COLUMN id SET DEFAULT nextval('public.schools_id_seq'::regclass);


--
-- Data for Name: analysis_jobs; Type: TABLE DATA; Schema: archive; Owner: postgres
--

COPY archive.analysis_jobs (id, job_id, user_id, status, result_id, error_message, completed_at, created_at, updated_at, assessment_name, priority, retry_count, max_retries, processing_started_at) FROM stdin;
a3dc9bd1-fa14-4a08-b835-35ed287e8b8c	ebc214f4-1e10-4d74-bd5b-8b0f475ca7c2	5bd0d9dd-8b5e-4d93-ba6f-d7b59a2f9b2f	processing	\N	\N	\N	2025-07-21 19:51:44.005+07	2025-07-21 19:51:44.239379+07	AI-Driven Talent Mapping	0	0	3	\N
c56cf673-1f1c-47d8-bca4-ce8ccae348da	e2a412c9-e94c-45a5-916b-7e3f2d9e009e	4cc10765-a456-4d7e-8a3a-39dbffd368fc	processing	\N	\N	\N	2025-07-21 19:51:44.11+07	2025-07-21 19:51:44.335106+07	AI-Driven Talent Mapping	0	0	3	\N
f5219cb7-8bb4-4b08-975d-2725707f143c	a94175d8-50b6-47c5-a4b5-555ebeed2ada	079a225a-b655-4059-8721-2621977cdb41	processing	\N	\N	\N	2025-07-21 19:51:44.313+07	2025-07-21 19:51:44.496464+07	AI-Driven Talent Mapping	0	0	3	\N
f25c3a07-e594-4c9d-9e0c-a0355b2be8b5	bb5236b1-d2eb-4fd4-af3f-47102fd0b137	befa73db-5391-414a-8c32-a0e1ea8ab029	processing	\N	\N	\N	2025-07-21 19:51:44.51+07	2025-07-21 19:51:44.642992+07	AI-Driven Talent Mapping	0	0	3	\N
ae49352b-96f3-45dc-b7c1-c775a5cef6b5	6c7f8a8d-804e-4bee-89fc-e84ce3b0bb11	a2b20c9e-c202-46b0-8544-a2621cecf4f1	processing	\N	\N	\N	2025-07-21 19:51:44.722+07	2025-07-21 19:51:44.845814+07	AI-Driven Talent Mapping	0	0	3	\N
a3bb32d2-868e-4f9b-b641-c75c787559b0	93c33b4a-c8f3-4fa5-9f52-21eaf03aff87	18fb7ba8-91a3-47ce-9c5e-2241171d42b2	processing	\N	\N	\N	2025-07-21 19:51:44.909+07	2025-07-21 19:51:45.039862+07	AI-Driven Talent Mapping	0	0	3	\N
d2401ce1-40d0-407a-839e-88da17fab22a	65ceecaa-14d5-4c94-8ce5-c0e76106e972	4cde4f55-5a7f-4e04-a4ad-48e1cbb3ab5e	processing	\N	\N	\N	2025-07-21 19:51:45.114+07	2025-07-21 19:51:45.243939+07	AI-Driven Talent Mapping	0	0	3	\N
21c105ac-b3d7-4971-9df2-72a19fb00eb8	22813e96-978a-4c34-ae7b-9c9c8352c311	6f064958-77f9-4aba-98d0-267a5d01b9e9	processing	\N	\N	\N	2025-07-21 19:51:45.31+07	2025-07-21 19:51:45.430505+07	AI-Driven Talent Mapping	0	0	3	\N
b5590954-aaf9-43f9-852a-bfe9a446fcbe	741990d8-a4f8-4e6c-9760-4d6b7b638c1a	42054333-c489-4bd2-8925-9e5d08409bcc	processing	\N	\N	\N	2025-07-21 19:51:45.507+07	2025-07-21 19:51:45.643631+07	AI-Driven Talent Mapping	0	0	3	\N
3194b1c8-8d1b-43bd-9bd7-63ef561fb825	02f54045-13eb-4286-b2b5-8f1d0e9bcd55	9c6a7c53-ea87-4f5e-97f2-f945aa07ac33	processing	\N	\N	\N	2025-07-21 19:51:45.696+07	2025-07-21 19:51:45.820123+07	AI-Driven Talent Mapping	0	0	3	\N
a2ff6e7b-18da-400b-b2e8-969f860612cc	5332791f-202e-47de-8a05-11bf912cd262	c0d97bd0-3fc7-4c8a-8484-c05f3c10cec0	processing	\N	\N	\N	2025-07-21 19:51:45.907+07	2025-07-21 19:51:46.040554+07	AI-Driven Talent Mapping	0	0	3	\N
37fe0989-a729-49b8-a388-46efacba8dc9	202c18e8-27fb-4558-b79d-b18ce928805a	fedc5841-aa5e-4c87-8a6b-3d0ab8ddc56d	processing	\N	\N	\N	2025-07-21 19:51:46.115+07	2025-07-21 19:51:46.255726+07	AI-Driven Talent Mapping	0	0	3	\N
b38391ef-98ac-4a52-8d19-687d85c000aa	34845f85-0fa5-441a-b64f-92fb9fe00a8c	bc197ca5-fb95-46e6-ba3e-5a4a675e4ff2	processing	\N	\N	\N	2025-07-21 19:51:46.305+07	2025-07-21 19:51:46.430481+07	AI-Driven Talent Mapping	0	0	3	\N
b61a99fe-13df-421d-85e3-667cd38c5522	8c255dad-f537-4ab1-a3a8-8ee89adba446	a459fdfd-dd97-40bf-a870-f19435532a55	processing	\N	\N	\N	2025-07-21 19:51:46.51+07	2025-07-21 19:51:46.63895+07	AI-Driven Talent Mapping	0	0	3	\N
f1dc84bc-2e27-453a-971b-876eee9d32c1	fa1e5494-e6ca-4458-858a-b92fd2176595	c19ef35e-f7bb-45d7-a217-4698f5c7f4e7	processing	\N	\N	\N	2025-07-21 19:51:46.707+07	2025-07-21 19:51:46.8712+07	AI-Driven Talent Mapping	0	0	3	\N
\.


--
-- Data for Name: analysis_results; Type: TABLE DATA; Schema: archive; Owner: postgres
--

COPY archive.analysis_results (id, user_id, assessment_data, persona_profile, status, error_message, created_at, updated_at, assessment_name) FROM stdin;
\.


--
-- Data for Name: idempotency_cache; Type: TABLE DATA; Schema: assessment; Owner: postgres
--

COPY assessment.idempotency_cache (id, idempotency_key, user_id, request_hash, response_data, status_code, created_at, expires_at) FROM stdin;
\.


--
-- Data for Name: user_profiles; Type: TABLE DATA; Schema: auth; Owner: postgres
--

COPY auth.user_profiles (user_id, full_name, date_of_birth, gender, school_id, created_at, updated_at) FROM stdin;
5bd0d9dd-8b5e-4d93-ba6f-d7b59a2f9b2f	Mass Test User 1	1995-01-01	male	\N	2025-07-21 19:51:37.031+07	2025-07-21 19:51:37.031+07
4cc10765-a456-4d7e-8a3a-39dbffd368fc	Mass Test User 2	1995-01-01	male	\N	2025-07-21 19:51:37.062+07	2025-07-21 19:51:37.062+07
079a225a-b655-4059-8721-2621977cdb41	Mass Test User 3	1995-01-01	male	\N	2025-07-21 19:51:37.232+07	2025-07-21 19:51:37.232+07
befa73db-5391-414a-8c32-a0e1ea8ab029	Mass Test User 4	1995-01-01	male	\N	2025-07-21 19:51:37.432+07	2025-07-21 19:51:37.432+07
a2b20c9e-c202-46b0-8544-a2621cecf4f1	Mass Test User 5	1995-01-01	male	\N	2025-07-21 19:51:37.629+07	2025-07-21 19:51:37.629+07
18fb7ba8-91a3-47ce-9c5e-2241171d42b2	Mass Test User 6	1995-01-01	male	\N	2025-07-21 19:51:37.868+07	2025-07-21 19:51:37.868+07
4cde4f55-5a7f-4e04-a4ad-48e1cbb3ab5e	Mass Test User 7	1995-01-01	male	\N	2025-07-21 19:51:38.046+07	2025-07-21 19:51:38.046+07
6f064958-77f9-4aba-98d0-267a5d01b9e9	Mass Test User 8	1995-01-01	male	\N	2025-07-21 19:51:38.246+07	2025-07-21 19:51:38.246+07
42054333-c489-4bd2-8925-9e5d08409bcc	Mass Test User 9	1995-01-01	male	\N	2025-07-21 19:51:38.418+07	2025-07-21 19:51:38.419+07
9c6a7c53-ea87-4f5e-97f2-f945aa07ac33	Mass Test User 10	1995-01-01	male	\N	2025-07-21 19:51:38.628+07	2025-07-21 19:51:38.628+07
c0d97bd0-3fc7-4c8a-8484-c05f3c10cec0	Mass Test User 11	1995-01-01	male	\N	2025-07-21 19:51:38.832+07	2025-07-21 19:51:38.832+07
fedc5841-aa5e-4c87-8a6b-3d0ab8ddc56d	Mass Test User 12	1995-01-01	male	\N	2025-07-21 19:51:39.027+07	2025-07-21 19:51:39.027+07
bc197ca5-fb95-46e6-ba3e-5a4a675e4ff2	Mass Test User 13	1995-01-01	male	\N	2025-07-21 19:51:39.247+07	2025-07-21 19:51:39.247+07
a459fdfd-dd97-40bf-a870-f19435532a55	Mass Test User 14	1995-01-01	male	\N	2025-07-21 19:51:39.443+07	2025-07-21 19:51:39.443+07
c19ef35e-f7bb-45d7-a217-4698f5c7f4e7	Mass Test User 15	1995-01-01	male	\N	2025-07-21 19:51:39.626+07	2025-07-21 19:51:39.626+07
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: postgres
--

COPY auth.users (id, username, email, password_hash, user_type, is_active, token_balance, last_login, created_at, updated_at) FROM stdin;
5bd0d9dd-8b5e-4d93-ba6f-d7b59a2f9b2f	massuser175310229668316876	<EMAIL>	$2b$10$.TIPkdzRSXOWT5LdHPh80uRdUPnpRGwJnEDk9NZnvu0xaol3/UDwy	user	t	2	2025-07-21 19:51:36.992+07	2025-07-21 19:51:36.895+07	2025-07-21 19:51:43.91742+07
4cc10765-a456-4d7e-8a3a-39dbffd368fc	massuser175310229688629606	<EMAIL>	$2b$10$3N1EfXJgPHVvp7XphwM5cuS8eabeFenlPa1hCYsJV4474b/87jYRO	user	t	2	2025-07-21 19:51:37.013+07	2025-07-21 19:51:36.947+07	2025-07-21 19:51:44.099828+07
079a225a-b655-4059-8721-2621977cdb41	massuser175310229708935762	<EMAIL>	$2b$10$OzGPCLx6pcMclX4LAJeqruFh6hsqKK7c1NICw8d/Qbvy9tYZ6Vh6u	user	t	2	2025-07-21 19:51:37.219+07	2025-07-21 19:51:37.155+07	2025-07-21 19:51:44.299891+07
befa73db-5391-414a-8c32-a0e1ea8ab029	massuser175310229729044571	<EMAIL>	$2b$10$YIQJs278hoI9bJ/kwhN5AuP3N3vUMUDxWZVbtw9TOgbZDyA1aCuDG	user	t	2	2025-07-21 19:51:37.418+07	2025-07-21 19:51:37.347+07	2025-07-21 19:51:44.498521+07
a2b20c9e-c202-46b0-8544-a2621cecf4f1	massuser17531022974925528	<EMAIL>	$2b$10$dBL/WOkH2gwAoSXc4DTI0.Z9s8crpDKJALc9d.5ObsNVoWvT7r6.q	user	t	2	2025-07-21 19:51:37.619+07	2025-07-21 19:51:37.547+07	2025-07-21 19:51:44.701668+07
18fb7ba8-91a3-47ce-9c5e-2241171d42b2	massuser175310229769369415	<EMAIL>	$2b$10$ZtasZUshJy7wNP/ePeR.heE5PW9lG/VbB3ksIvFI8dxUd7htHj9Ii	user	t	2	2025-07-21 19:51:37.842+07	2025-07-21 19:51:37.749+07	2025-07-21 19:51:44.89863+07
4cde4f55-5a7f-4e04-a4ad-48e1cbb3ab5e	massuser175310229789679909	<EMAIL>	$2b$10$02o0GunWbdaKIbOgM5EYLurTsRzCKYUfgpDkHJv4TPjUHPs4ncNi6	user	t	2	2025-07-21 19:51:38.034+07	2025-07-21 19:51:37.973+07	2025-07-21 19:51:45.105017+07
6f064958-77f9-4aba-98d0-267a5d01b9e9	massuser17531022980978126	<EMAIL>	$2b$10$w0d8ZsfFlnrp25C8yHo8bO6I2Ju5hzpARi7l5p1b/qHDsB7G722Ca	user	t	2	2025-07-21 19:51:38.233+07	2025-07-21 19:51:38.163+07	2025-07-21 19:51:45.301403+07
42054333-c489-4bd2-8925-9e5d08409bcc	massuser175310229828395315	<EMAIL>	$2b$10$yxTBrRLXqTS3O03LsLqqZ.j/Qb0KD/1veODqdBLImf7omtLtumcQW	user	t	2	2025-07-21 19:51:38.404+07	2025-07-21 19:51:38.339+07	2025-07-21 19:51:45.49411+07
9c6a7c53-ea87-4f5e-97f2-f945aa07ac33	massuser1753102298485107491	<EMAIL>	$2b$10$1iXEyGPX5.miVNPUtD84uO9d/Q0JmREwOdmSjq0pVzEOKYpZ488Xi	user	t	2	2025-07-21 19:51:38.615+07	2025-07-21 19:51:38.552+07	2025-07-21 19:51:45.687075+07
c0d97bd0-3fc7-4c8a-8484-c05f3c10cec0	massuser1753102298690117489	<EMAIL>	$2b$10$Umo/.lrbdTyUgFu9BL.ZOuVatBVFo87lcZwWXHsMnQfWKDlplweWO	user	t	2	2025-07-21 19:51:38.82+07	2025-07-21 19:51:38.752+07	2025-07-21 19:51:45.892652+07
fedc5841-aa5e-4c87-8a6b-3d0ab8ddc56d	massuser175310229889112377	<EMAIL>	$2b$10$sLpRQcELO7jgi/TioV48mOshuBwki5wUTRYAQO6jlDSFEV7Mrs6lC	user	t	2	2025-07-21 19:51:39.016+07	2025-07-21 19:51:38.947+07	2025-07-21 19:51:46.097727+07
bc197ca5-fb95-46e6-ba3e-5a4a675e4ff2	massuser1753102299091134076	<EMAIL>	$2b$10$6grNPfc3XNSDRUToYhRbg.gM3hTSTQaUGX1vr68K8B4gIOjq7eNbe	user	t	2	2025-07-21 19:51:39.228+07	2025-07-21 19:51:39.153+07	2025-07-21 19:51:46.295518+07
a459fdfd-dd97-40bf-a870-f19435532a55	massuser1753102299291143946	<EMAIL>	$2b$10$H1/XweORnQ9S27VnojT.6u7QRSV7TYUni/UD7oggLVLDL5QqooEci	user	t	2	2025-07-21 19:51:39.43+07	2025-07-21 19:51:39.358+07	2025-07-21 19:51:46.498897+07
c19ef35e-f7bb-45d7-a217-4698f5c7f4e7	massuser1753102299491153735	<EMAIL>	$2b$10$M9UjTezpRzQPAPTSf8brrui2VL0kWniaqZ5NuWTctb.GRP0alaZTW	user	t	2	2025-07-21 19:51:39.613+07	2025-07-21 19:51:39.551+07	2025-07-21 19:51:46.697794+07
\.


--
-- Data for Name: schools; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.schools (id, name, address, city, province, created_at) FROM stdin;
\.


--
-- Name: schools_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.schools_id_seq', 1, false);


--
-- Name: analysis_jobs analysis_jobs_job_id_key; Type: CONSTRAINT; Schema: archive; Owner: postgres
--

ALTER TABLE ONLY archive.analysis_jobs
    ADD CONSTRAINT analysis_jobs_job_id_key UNIQUE (job_id);


--
-- Name: analysis_jobs analysis_jobs_pkey; Type: CONSTRAINT; Schema: archive; Owner: postgres
--

ALTER TABLE ONLY archive.analysis_jobs
    ADD CONSTRAINT analysis_jobs_pkey PRIMARY KEY (id);


--
-- Name: analysis_results analysis_results_pkey; Type: CONSTRAINT; Schema: archive; Owner: postgres
--

ALTER TABLE ONLY archive.analysis_results
    ADD CONSTRAINT analysis_results_pkey PRIMARY KEY (id);


--
-- Name: idempotency_cache idempotency_cache_idempotency_key_key; Type: CONSTRAINT; Schema: assessment; Owner: postgres
--

ALTER TABLE ONLY assessment.idempotency_cache
    ADD CONSTRAINT idempotency_cache_idempotency_key_key UNIQUE (idempotency_key);


--
-- Name: idempotency_cache idempotency_cache_pkey; Type: CONSTRAINT; Schema: assessment; Owner: postgres
--

ALTER TABLE ONLY assessment.idempotency_cache
    ADD CONSTRAINT idempotency_cache_pkey PRIMARY KEY (id);


--
-- Name: user_profiles user_profiles_pkey; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.user_profiles
    ADD CONSTRAINT user_profiles_pkey PRIMARY KEY (user_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: schools schools_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schools
    ADD CONSTRAINT schools_pkey PRIMARY KEY (id);


--
-- Name: idx_analysis_jobs_assessment_name; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_assessment_name ON archive.analysis_jobs USING btree (assessment_name);


--
-- Name: idx_analysis_jobs_created_at; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_created_at ON archive.analysis_jobs USING btree (created_at);


--
-- Name: idx_analysis_jobs_job_id; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE UNIQUE INDEX idx_analysis_jobs_job_id ON archive.analysis_jobs USING btree (job_id);


--
-- Name: idx_analysis_jobs_queue_processing; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_queue_processing ON archive.analysis_jobs USING btree (status, priority, created_at);


--
-- Name: idx_analysis_jobs_retry_logic; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_retry_logic ON archive.analysis_jobs USING btree (status, retry_count, max_retries);


--
-- Name: idx_analysis_jobs_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_status ON archive.analysis_jobs USING btree (status);


--
-- Name: idx_analysis_jobs_user_id; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_user_id ON archive.analysis_jobs USING btree (user_id);


--
-- Name: idx_analysis_jobs_user_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_user_status ON archive.analysis_jobs USING btree (user_id, status);


--
-- Name: idx_analysis_jobs_user_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_user_status_created ON archive.analysis_jobs USING btree (user_id, status, created_at);


--
-- Name: idx_analysis_results_archetype_assessment; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_archetype_assessment ON archive.analysis_results USING btree (((persona_profile ->> 'archetype'::text)), assessment_name);


--
-- Name: idx_analysis_results_archetype_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_archetype_status_created ON archive.analysis_results USING btree (((persona_profile ->> 'archetype'::text)), status, created_at);


--
-- Name: idx_analysis_results_assessment_data_gin; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_assessment_data_gin ON archive.analysis_results USING gin (assessment_data);


--
-- Name: idx_analysis_results_assessment_name; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_assessment_name ON archive.analysis_results USING btree (assessment_name);


--
-- Name: idx_analysis_results_assessment_name_optimized; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_assessment_name_optimized ON archive.analysis_results USING btree (assessment_name);


--
-- Name: idx_analysis_results_created_at; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_created_at ON archive.analysis_results USING btree (created_at);


--
-- Name: idx_analysis_results_persona_profile_gin; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_persona_profile_gin ON archive.analysis_results USING gin (persona_profile);


--
-- Name: idx_analysis_results_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_status ON archive.analysis_results USING btree (status);


--
-- Name: idx_analysis_results_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_status_created ON archive.analysis_results USING btree (status, created_at);


--
-- Name: idx_analysis_results_user_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_created ON archive.analysis_results USING btree (user_id, created_at);


--
-- Name: idx_analysis_results_user_id; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_id ON archive.analysis_results USING btree (user_id);


--
-- Name: idx_analysis_results_user_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_status ON archive.analysis_results USING btree (user_id, status);


--
-- Name: idx_analysis_results_user_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_status_created ON archive.analysis_results USING btree (user_id, status, created_at);


--
-- Name: idx_persona_profile_archetype; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_archetype ON archive.analysis_results USING gin (((persona_profile ->> 'archetype'::text)));


--
-- Name: idx_persona_profile_career_recommendations; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_career_recommendations ON archive.analysis_results USING gin (((persona_profile -> 'careerRecommendations'::text)));


--
-- Name: idx_persona_profile_ocean; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_ocean ON archive.analysis_results USING gin (((persona_profile -> 'ocean'::text)));


--
-- Name: idx_persona_profile_riasec; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_riasec ON archive.analysis_results USING gin (((persona_profile -> 'riasec'::text)));


--
-- Name: idx_persona_profile_risk_tolerance; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_risk_tolerance ON archive.analysis_results USING gin (((persona_profile ->> 'riskTolerance'::text)));


--
-- Name: idx_persona_profile_strengths; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_strengths ON archive.analysis_results USING gin (((persona_profile -> 'strengths'::text)));


--
-- Name: idx_persona_profile_weaknesses; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_weaknesses ON archive.analysis_results USING gin (((persona_profile -> 'weaknesses'::text)));


--
-- Name: idx_idempotency_cache_expires_at; Type: INDEX; Schema: assessment; Owner: postgres
--

CREATE INDEX idx_idempotency_cache_expires_at ON assessment.idempotency_cache USING btree (expires_at);


--
-- Name: idx_idempotency_cache_key; Type: INDEX; Schema: assessment; Owner: postgres
--

CREATE INDEX idx_idempotency_cache_key ON assessment.idempotency_cache USING btree (idempotency_key);


--
-- Name: idx_idempotency_cache_user_id; Type: INDEX; Schema: assessment; Owner: postgres
--

CREATE INDEX idx_idempotency_cache_user_id ON assessment.idempotency_cache USING btree (user_id);


--
-- Name: idx_user_profiles_created_at; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_created_at ON auth.user_profiles USING btree (created_at);


--
-- Name: idx_user_profiles_school_id; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_school_id ON auth.user_profiles USING btree (school_id);


--
-- Name: idx_user_profiles_school_id_idx; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_school_id_idx ON auth.user_profiles USING btree (school_id);


--
-- Name: idx_user_profiles_user_id; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_user_id ON auth.user_profiles USING btree (user_id);


--
-- Name: idx_users_admin_lookup; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_admin_lookup ON auth.users USING btree (user_type, is_active, email);


--
-- Name: idx_users_created_at; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_created_at ON auth.users USING btree (created_at);


--
-- Name: idx_users_email; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE UNIQUE INDEX idx_users_email ON auth.users USING btree (email);


--
-- Name: idx_users_is_active; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_is_active ON auth.users USING btree (is_active);


--
-- Name: idx_users_user_type; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_user_type ON auth.users USING btree (user_type);


--
-- Name: idx_users_username; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE UNIQUE INDEX idx_users_username ON auth.users USING btree (username) WHERE (username IS NOT NULL);


--
-- Name: idx_schools_city; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_city ON public.schools USING btree (city);


--
-- Name: idx_schools_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_created_at ON public.schools USING btree (created_at);


--
-- Name: idx_schools_full_info; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_full_info ON public.schools USING btree (name, city, province);


--
-- Name: idx_schools_location; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_location ON public.schools USING btree (province, city);


--
-- Name: idx_schools_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_name ON public.schools USING btree (name);


--
-- Name: idx_schools_province; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_province ON public.schools USING btree (province);


--
-- Name: analysis_jobs update_analysis_jobs_updated_at; Type: TRIGGER; Schema: archive; Owner: postgres
--

CREATE TRIGGER update_analysis_jobs_updated_at BEFORE UPDATE ON archive.analysis_jobs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: analysis_results update_analysis_results_updated_at; Type: TRIGGER; Schema: archive; Owner: postgres
--

CREATE TRIGGER update_analysis_results_updated_at BEFORE UPDATE ON archive.analysis_results FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_profiles update_user_profiles_updated_at; Type: TRIGGER; Schema: auth; Owner: postgres
--

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON auth.user_profiles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: auth; Owner: postgres
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON auth.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_profiles user_profiles_school_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.user_profiles
    ADD CONSTRAINT user_profiles_school_id_fkey FOREIGN KEY (school_id) REFERENCES public.schools(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_profiles user_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.user_profiles
    ADD CONSTRAINT user_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: SCHEMA archive; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA archive TO PUBLIC;


--
-- Name: SCHEMA assessment; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA assessment TO PUBLIC;


--
-- Name: SCHEMA auth; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA auth TO PUBLIC;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO atma_user;


--
-- Name: TABLE analysis_jobs; Type: ACL; Schema: archive; Owner: postgres
--

GRANT ALL ON TABLE archive.analysis_jobs TO PUBLIC;


--
-- Name: TABLE analysis_results; Type: ACL; Schema: archive; Owner: postgres
--

GRANT ALL ON TABLE archive.analysis_results TO PUBLIC;


--
-- Name: TABLE idempotency_cache; Type: ACL; Schema: assessment; Owner: postgres
--

GRANT SELECT ON TABLE assessment.idempotency_cache TO atma_user;


--
-- Name: TABLE user_profiles; Type: ACL; Schema: auth; Owner: postgres
--

GRANT ALL ON TABLE auth.user_profiles TO PUBLIC;


--
-- Name: TABLE users; Type: ACL; Schema: auth; Owner: postgres
--

GRANT ALL ON TABLE auth.users TO PUBLIC;


--
-- Name: TABLE schools; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.schools TO atma_user;
GRANT ALL ON TABLE public.schools TO PUBLIC;


--
-- Name: SEQUENCE schools_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.schools_id_seq TO atma_user;
GRANT ALL ON SEQUENCE public.schools_id_seq TO PUBLIC;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO atma_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO atma_user;


--
-- PostgreSQL database dump complete
--

